const std = @import("std");
const device = @import("device.zig");
const tui = @import("tui.zig");
const test_module = @import("test.zig");
const report = @import("report.zig");

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();

    // 打印欢迎信息
    try printWelcome();

    // 枚举存储设备
    const devices = try device.enumerateDevices(allocator);
    defer allocator.free(devices);

    if (devices.len == 0) {
        std.debug.print("未找到可用的存储设备。\n", .{});
        return;
    }

    // 显示TUI选择界面
    const selected_device = try tui.selectDevice(devices);
    if (selected_device == null) {
        std.debug.print("未选择设备，退出程序。\n", .{});
        return;
    }

    const device_info = selected_device.?;

    // 确认操作
    const confirmed = try tui.confirmOperation(device_info);
    if (!confirmed) {
        std.debug.print("操作已取消。\n", .{});
        return;
    }

    // 执行存储测试
    std.debug.print("开始测试设备: {s}\n", .{device_info.name});
    const test_result = try test_module.runStorageTest(allocator, device_info);

    // 生成报告
    try report.generateReport(allocator, device_info, test_result);
}

fn printWelcome() !void {
    std.debug.print("\n", .{});
    std.debug.print("╔══════════════════════════════════════════════════════════════╗\n", .{});
    std.debug.print("║                    SD卡真实容量检测工具                      ║\n", .{});
    std.debug.print("║                      SD Card Checker                        ║\n", .{});
    std.debug.print("╚══════════════════════════════════════════════════════════════╝\n", .{});
    std.debug.print("\n", .{});
    std.debug.print("警告: 此工具将向选定设备写入测试数据，可能会覆盖现有数据！\n", .{});
    std.debug.print("请确保已备份重要数据后再继续。\n\n", .{});
}
