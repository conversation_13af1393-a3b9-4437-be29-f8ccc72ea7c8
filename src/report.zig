const std = @import("std");
const device = @import("device.zig");
const test_module = @import("test.zig");
const hash = @import("hash.zig");
const tui = @import("tui.zig");

pub fn generateReport(allocator: std.mem.Allocator, _: device.DeviceInfo, test_result: test_module.TestResult) !void {
    // 显示控制台报告
    try displayConsoleReport(allocator, test_result);

    // 生成文件报告
    try generateFileReport(allocator, test_result);
}

fn displayConsoleReport(_: std.mem.Allocator, test_result: test_module.TestResult) !void {
    std.debug.print("\x1B[2J\x1B[H", .{});

    std.debug.print("\n");
    std.debug.print("╔══════════════════════════════════════════════════════════════╗\n");
    std.debug.print("║                        测试报告                              ║\n");
    std.debug.print("╚══════════════════════════════════════════════════════════════╝\n");
    std.debug.print("\n");

    // 设备信息
    std.debug.print("设备信息:\n");
    std.debug.print("  设备名称: {s}\n", .{test_result.device_info.name});
    std.debug.print("  设备路径: {s}\n", .{test_result.device_info.path});
    std.debug.print("  设备型号: {s}\n", .{test_result.device_info.model});
    std.debug.print("  设备厂商: {s}\n", .{test_result.device_info.vendor});
    std.debug.print("\n");

    // 容量信息
    const claimed_size_str = device.formatSize(test_result.claimed_size);
    const actual_size_str = device.formatSize(test_result.actual_size);

    std.debug.print("容量信息:\n");
    std.debug.print("  声明容量: {s}\n", .{claimed_size_str});
    std.debug.print("  实际容量: {s}\n", .{actual_size_str});

    if (test_result.is_fake) {
        const fake_percentage = ((test_result.claimed_size - test_result.actual_size) * 100) / test_result.claimed_size;
        std.debug.print("  容量损失: {d}%\n", .{fake_percentage});
        tui.displayWarning("检测到假冒存储设备！");
    } else {
        tui.displaySuccess("设备容量正常");
    }
    std.debug.print("\n");

    // 测试结果详情
    std.debug.print("测试详情:\n");
    std.debug.print("  测试时长: {d} 秒\n", .{test_result.test_duration / 1000});
    std.debug.print("  错误数量: {d}\n", .{test_result.total_errors});
    std.debug.print("\n");

    // 各模式测试结果
    std.debug.print("各测试模式结果:\n");
    for (test_result.test_patterns) |pattern| {
        const pattern_name = switch (pattern.pattern_type) {
            .sequential => "顺序模式",
            .random => "随机模式",
            .alternating => "交替模式",
            .zeros => "全零模式",
            .ones => "全一模式",
        };

        const status = if (pattern.matches) "✅ 通过" else "❌ 失败";
        std.debug.print("  {s}: {s}", .{ pattern_name, status });

        if (pattern.error_position) |pos| {
            const pos_str = device.formatSize(pos);
            std.debug.print(" (错误位置: {s})", .{pos_str});
        }
        std.debug.print("\n");
    }

    std.debug.print("\n");

    // 建议
    if (test_result.is_fake) {
        std.debug.print("建议:\n");
        std.debug.print("  ⚠️  此设备可能是假冒产品，实际容量小于声明容量\n");
        std.debug.print("  ⚠️  建议停止使用此设备存储重要数据\n");
        std.debug.print("  ⚠️  如果是新购买的设备，建议联系卖家退换货\n");
    } else {
        std.debug.print("建议:\n");
        std.debug.print("  ✅ 设备容量正常，可以安全使用\n");
    }

    std.debug.print("\n按回车键继续...\n");
    _ = try readUserInput();
}

fn generateFileReport(allocator: std.mem.Allocator, test_result: test_module.TestResult) !void {
    const timestamp = std.time.timestamp();
    const filename = try std.fmt.allocPrint(allocator, "sdchk_report_{d}.txt", .{timestamp});
    defer allocator.free(filename);

    const file = std.fs.cwd().createFile(filename, .{}) catch {
        tui.displayError("无法创建报告文件");
        return;
    };
    defer file.close();

    const writer = file.writer();

    // 写入报告头
    try writer.print("SD卡容量检测报告\n");
    try writer.print("================\n\n");

    // 生成时间
    const datetime = std.time.epoch.EpochSeconds{ .secs = @as(u64, @intCast(timestamp)) };
    try writer.print("生成时间: {}\n\n", .{datetime});

    // 设备信息
    try writer.print("设备信息:\n");
    try writer.print("  设备名称: {s}\n", .{test_result.device_info.name});
    try writer.print("  设备路径: {s}\n", .{test_result.device_info.path});
    try writer.print("  设备型号: {s}\n", .{test_result.device_info.model});
    try writer.print("  设备厂商: {s}\n", .{test_result.device_info.vendor});
    try writer.print("  可移动设备: {s}\n\n", .{if (test_result.device_info.removable) "是" else "否"});

    // 测试结果
    try writer.print("测试结果:\n");
    try writer.print("  声明容量: {} 字节 ({})\n", .{ test_result.claimed_size, device.formatSize(test_result.claimed_size) });
    try writer.print("  实际容量: {} 字节 ({})\n", .{ test_result.actual_size, device.formatSize(test_result.actual_size) });
    try writer.print("  测试时长: {} 毫秒\n", .{test_result.test_duration});
    try writer.print("  错误数量: {}\n", .{test_result.total_errors});
    try writer.print("  是否假冒: {s}\n\n", .{if (test_result.is_fake) "是" else "否"});

    // 详细测试结果
    try writer.print("详细测试结果:\n");
    for (test_result.test_patterns) |pattern| {
        const pattern_name = switch (pattern.pattern_type) {
            .sequential => "顺序模式",
            .random => "随机模式",
            .alternating => "交替模式",
            .zeros => "全零模式",
            .ones => "全一模式",
        };

        try writer.print("  {s}:\n", .{pattern_name});
        try writer.print("    结果: {s}\n", .{if (pattern.matches) "通过" else "失败"});

        if (pattern.error_position) |pos| {
            try writer.print("    错误位置: {} 字节\n", .{pos});
        }

        // 写入哈希值
        const written_hex = try pattern.written_hash.toHexString(allocator);
        defer allocator.free(written_hex);
        const read_hex = try pattern.read_hash.toHexString(allocator);
        defer allocator.free(read_hex);

        try writer.print("    写入哈希: {s}\n", .{written_hex});
        try writer.print("    读取哈希: {s}\n", .{read_hex});
        try writer.print("\n");
    }

    // 结论和建议
    try writer.print("结论和建议:\n");
    if (test_result.is_fake) {
        const fake_percentage = ((test_result.claimed_size - test_result.actual_size) * 100) / test_result.claimed_size;
        try writer.print("  此设备可能是假冒产品，容量损失约 {}%\n", .{fake_percentage});
        try writer.print("  建议停止使用此设备存储重要数据\n");
        try writer.print("  如果是新购买的设备，建议联系卖家退换货\n");
    } else {
        try writer.print("  设备容量正常，可以安全使用\n");
    }

    tui.displaySuccess(try std.fmt.allocPrint(allocator, "报告已保存到文件: {s}", .{filename}));
}

fn readUserInput() ![]u8 {
    const stdin = std.io.getStdIn().reader();
    var buffer: [256]u8 = undefined;

    if (try stdin.readUntilDelimiterOrEof(buffer[0..], '\n')) |input| {
        return std.heap.page_allocator.dupe(u8, input);
    } else {
        return std.heap.page_allocator.dupe(u8, "");
    }
}
