const std = @import("std");

pub const DeviceInfo = struct {
    name: []const u8,
    path: []const u8,
    size: u64,
    model: []const u8,
    vendor: []const u8,
    removable: bool,
    
    pub fn deinit(self: *DeviceInfo, allocator: std.mem.Allocator) void {
        allocator.free(self.name);
        allocator.free(self.path);
        allocator.free(self.model);
        allocator.free(self.vendor);
    }
};

pub fn enumerateDevices(allocator: std.mem.Allocator) ![]DeviceInfo {
    var devices = std.ArrayList(DeviceInfo).init(allocator);
    defer devices.deinit();

    // 在Linux系统中，扫描/sys/block目录来找到块设备
    const block_dir = "/sys/block";
    var dir = std.fs.openDirAbsolute(block_dir, .{ .iterate = true }) catch |err| {
        std.debug.print("无法打开 {s}: {}\n", .{ block_dir, err });
        return try devices.toOwnedSlice();
    };
    defer dir.close();

    var iterator = dir.iterate();
    while (try iterator.next()) |entry| {
        if (entry.kind != .directory) continue;
        
        // 跳过循环设备和RAM磁盘
        if (std.mem.startsWith(u8, entry.name, "loop") or 
            std.mem.startsWith(u8, entry.name, "ram")) {
            continue;
        }

        const device_info = parseDeviceInfo(allocator, entry.name) catch |err| {
            std.debug.print("解析设备 {s} 信息失败: {}\n", .{ entry.name, err });
            continue;
        };

        try devices.append(device_info);
    }

    return try devices.toOwnedSlice();
}

fn parseDeviceInfo(allocator: std.mem.Allocator, device_name: []const u8) !DeviceInfo {
    var arena = std.heap.ArenaAllocator.init(allocator);
    defer arena.deinit();
    const temp_allocator = arena.allocator();

    // 构建设备路径
    const device_path = try std.fmt.allocPrint(allocator, "/dev/{s}", .{device_name});
    
    // 读取设备大小
    const size_path = try std.fmt.allocPrint(temp_allocator, "/sys/block/{s}/size", .{device_name});
    const size = readDeviceSize(size_path) catch 0;

    // 读取设备型号
    const model_path = try std.fmt.allocPrint(temp_allocator, "/sys/block/{s}/device/model", .{device_name});
    const model = readDeviceAttribute(allocator, model_path) catch try allocator.dupe(u8, "Unknown");

    // 读取设备厂商
    const vendor_path = try std.fmt.allocPrint(temp_allocator, "/sys/block/{s}/device/vendor", .{device_name});
    const vendor = readDeviceAttribute(allocator, vendor_path) catch try allocator.dupe(u8, "Unknown");

    // 检查是否为可移动设备
    const removable_path = try std.fmt.allocPrint(temp_allocator, "/sys/block/{s}/removable", .{device_name});
    const removable = readRemovableFlag(removable_path) catch false;

    return DeviceInfo{
        .name = try allocator.dupe(u8, device_name),
        .path = device_path,
        .size = size * 512, // 扇区大小通常为512字节
        .model = model,
        .vendor = vendor,
        .removable = removable,
    };
}

fn readDeviceSize(path: []const u8) !u64 {
    const file = std.fs.openFileAbsolute(path, .{}) catch return error.CannotReadSize;
    defer file.close();

    var buffer: [64]u8 = undefined;
    const bytes_read = try file.readAll(&buffer);
    const content = std.mem.trim(u8, buffer[0..bytes_read], " \n\r\t");
    
    return std.fmt.parseInt(u64, content, 10) catch return error.InvalidSize;
}

fn readDeviceAttribute(allocator: std.mem.Allocator, path: []const u8) ![]u8 {
    const file = std.fs.openFileAbsolute(path, .{}) catch return error.CannotReadAttribute;
    defer file.close();

    var buffer: [256]u8 = undefined;
    const bytes_read = try file.readAll(&buffer);
    const content = std.mem.trim(u8, buffer[0..bytes_read], " \n\r\t");
    
    return try allocator.dupe(u8, content);
}

fn readRemovableFlag(path: []const u8) !bool {
    const file = std.fs.openFileAbsolute(path, .{}) catch return false;
    defer file.close();

    var buffer: [8]u8 = undefined;
    const bytes_read = try file.readAll(&buffer);
    const content = std.mem.trim(u8, buffer[0..bytes_read], " \n\r\t");
    
    return std.mem.eql(u8, content, "1");
}

pub fn formatSize(size: u64) [32]u8 {
    var buffer: [32]u8 = undefined;
    
    if (size >= 1024 * 1024 * 1024 * 1024) {
        const tb = @as(f64, @floatFromInt(size)) / (1024.0 * 1024.0 * 1024.0 * 1024.0);
        _ = std.fmt.bufPrint(&buffer, "{d:.2} TB", .{tb}) catch unreachable;
    } else if (size >= 1024 * 1024 * 1024) {
        const gb = @as(f64, @floatFromInt(size)) / (1024.0 * 1024.0 * 1024.0);
        _ = std.fmt.bufPrint(&buffer, "{d:.2} GB", .{gb}) catch unreachable;
    } else if (size >= 1024 * 1024) {
        const mb = @as(f64, @floatFromInt(size)) / (1024.0 * 1024.0);
        _ = std.fmt.bufPrint(&buffer, "{d:.2} MB", .{mb}) catch unreachable;
    } else if (size >= 1024) {
        const kb = @as(f64, @floatFromInt(size)) / 1024.0;
        _ = std.fmt.bufPrint(&buffer, "{d:.2} KB", .{kb}) catch unreachable;
    } else {
        _ = std.fmt.bufPrint(&buffer, "{d} B", .{size}) catch unreachable;
    }
    
    return buffer;
}
