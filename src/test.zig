const std = @import("std");
const device = @import("device.zig");
const hash = @import("hash.zig");
const tui = @import("tui.zig");

pub const TestResult = struct {
    device_info: device.DeviceInfo,
    claimed_size: u64,
    actual_size: u64,
    test_patterns: []PatternTestResult,
    total_errors: u64,
    test_duration: u64, // 毫秒
    is_fake: bool,

    pub fn deinit(self: *TestResult, allocator: std.mem.Allocator) void {
        for (self.test_patterns) |*pattern| {
            pattern.deinit(allocator);
        }
        allocator.free(self.test_patterns);
    }
};

pub const PatternTestResult = struct {
    pattern_type: hash.PatternType,
    written_hash: hash.HashResult,
    read_hash: hash.HashResult,
    matches: bool,
    error_position: ?u64,

    pub fn deinit(self: *PatternTestResult, allocator: std.mem.Allocator) void {
        self.written_hash.deinit(allocator);
        self.read_hash.deinit(allocator);
    }
};

const CHUNK_SIZE = 1024 * 1024; // 1MB chunks
const TEST_PATTERNS = [_]hash.PatternType{ .sequential, .random, .alternating, .zeros, .ones };

pub fn runStorageTest(allocator: std.mem.Allocator, device_info: device.DeviceInfo) !TestResult {
    const start_time = std.time.milliTimestamp();

    tui.showTestPhase("开始存储容量测试");

    var test_results = std.ArrayList(PatternTestResult).init(allocator);
    defer test_results.deinit();

    var total_errors: u64 = 0;
    var actual_size: u64 = 0;

    // 打开设备文件
    const file = std.fs.openFileAbsolute(device_info.path, .{ .mode = .read_write }) catch {
        tui.displayError("无法打开设备文件，请检查权限");
        return error.DeviceAccessError;
    };
    defer file.close();

    // 对每种测试模式进行测试
    for (TEST_PATTERNS) |pattern_type| {
        const pattern_name = switch (pattern_type) {
            .sequential => "顺序模式",
            .random => "随机模式",
            .alternating => "交替模式",
            .zeros => "全零模式",
            .ones => "全一模式",
        };

        tui.showTestPhase(try std.fmt.allocPrint(allocator, "测试 {s}", .{pattern_name}));

        const pattern_result = try testPattern(allocator, file, device_info.size, pattern_type);
        try test_results.append(pattern_result);

        if (!pattern_result.matches) {
            total_errors += 1;
            if (pattern_result.error_position) |pos| {
                if (actual_size == 0 or pos < actual_size) {
                    actual_size = pos;
                }
            }
        }
    }

    // 如果没有发现错误，实际大小等于声明大小
    if (actual_size == 0) {
        actual_size = device_info.size;
    }

    const end_time = std.time.milliTimestamp();
    const duration = @as(u64, @intCast(end_time - start_time));

    return TestResult{
        .device_info = device_info,
        .claimed_size = device_info.size,
        .actual_size = actual_size,
        .test_patterns = try test_results.toOwnedSlice(),
        .total_errors = total_errors,
        .test_duration = duration,
        .is_fake = total_errors > 0,
    };
}

fn testPattern(allocator: std.mem.Allocator, file: std.fs.File, device_size: u64, pattern_type: hash.PatternType) !PatternTestResult {
    // 生成测试数据
    const test_data = try hash.generateTestPattern(allocator, CHUNK_SIZE, pattern_type);
    defer allocator.free(test_data);

    // 计算写入数据的哈希
    const written_hash = try hash.calculateSHA256(allocator, test_data);

    var total_written: u64 = 0;
    var error_position: ?u64 = null;

    // 写入阶段
    try file.seekTo(0);
    while (total_written < device_size) {
        const remaining = device_size - total_written;
        const write_size = std.math.min(remaining, CHUNK_SIZE);

        const bytes_written = file.write(test_data[0..write_size]) catch {
            error_position = total_written;
            break;
        };

        if (bytes_written != write_size) {
            error_position = total_written + bytes_written;
            break;
        }

        total_written += bytes_written;
        tui.showProgress(total_written, device_size, "写入数据");
    }

    std.debug.print("\n");

    // 读取阶段
    try file.seekTo(0);
    var read_buffer = try allocator.alloc(u8, CHUNK_SIZE);
    defer allocator.free(read_buffer);

    var hasher = std.crypto.hash.sha2.Sha256.init(.{});
    var total_read: u64 = 0;

    while (total_read < total_written) {
        const remaining = total_written - total_read;
        const read_size = std.math.min(remaining, CHUNK_SIZE);

        const bytes_read = file.read(read_buffer[0..read_size]) catch {
            if (error_position == null) {
                error_position = total_read;
            }
            break;
        };

        if (bytes_read != read_size) {
            if (error_position == null) {
                error_position = total_read + bytes_read;
            }
            break;
        }

        // 验证读取的数据
        if (!std.mem.eql(u8, read_buffer[0..bytes_read], test_data[0..bytes_read])) {
            if (error_position == null) {
                error_position = total_read;
            }
        }

        hasher.update(read_buffer[0..bytes_read]);
        total_read += bytes_read;
        tui.showProgress(total_read, total_written, "读取验证");
    }

    std.debug.print("\n");

    // 计算读取数据的哈希
    const read_digest = try allocator.alloc(u8, std.crypto.hash.sha2.Sha256.digest_length);
    hasher.final(read_digest);

    const read_hash = hash.HashResult{
        .hash_type = .sha256,
        .digest = read_digest,
    };

    const matches = hash.compareHashes(written_hash, read_hash) and error_position == null;

    return PatternTestResult{
        .pattern_type = pattern_type,
        .written_hash = written_hash,
        .read_hash = read_hash,
        .matches = matches,
        .error_position = error_position,
    };
}
