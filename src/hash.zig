const std = @import("std");

pub const HashType = enum {
    sha256,
    md5,
    crc32,
};

pub const HashResult = struct {
    hash_type: HashType,
    digest: []u8,

    pub fn deinit(self: *HashResult, allocator: std.mem.Allocator) void {
        allocator.free(self.digest);
    }

    pub fn toHexString(self: HashResult, allocator: std.mem.Allocator) ![]u8 {
        var hex_string = try allocator.alloc(u8, self.digest.len * 2);
        for (self.digest, 0..) |byte, i| {
            _ = std.fmt.bufPrint(hex_string[i * 2 .. i * 2 + 2], "{x:0>2}", .{byte}) catch unreachable;
        }
        return hex_string;
    }
};

pub fn calculateSHA256(allocator: std.mem.Allocator, data: []const u8) !HashResult {
    var hasher = std.crypto.hash.sha2.Sha256.init(.{});
    hasher.update(data);

    var digest_array: [std.crypto.hash.sha2.Sha256.digest_length]u8 = undefined;
    hasher.final(&digest_array);

    const digest = try allocator.dupe(u8, &digest_array);

    return HashResult{
        .hash_type = .sha256,
        .digest = digest,
    };
}

pub fn calculateMD5(allocator: std.mem.Allocator, data: []const u8) !HashResult {
    var hasher = std.crypto.hash.Md5.init();
    hasher.update(data);

    var digest_array: [std.crypto.hash.Md5.digest_length]u8 = undefined;
    hasher.final(&digest_array);

    const digest = try allocator.dupe(u8, &digest_array);

    return HashResult{
        .hash_type = .md5,
        .digest = digest,
    };
}

pub fn calculateCRC32(allocator: std.mem.Allocator, data: []const u8) !HashResult {
    const crc = std.hash.Crc32.hash(data);
    var digest = try allocator.alloc(u8, 4);
    std.mem.writeInt(u32, digest[0..4], crc, .big);

    return HashResult{
        .hash_type = .crc32,
        .digest = digest,
    };
}

pub fn calculateFileHash(allocator: std.mem.Allocator, file_path: []const u8, hash_type: HashType) !HashResult {
    const file = try std.fs.openFileAbsolute(file_path, .{});
    defer file.close();

    const file_size = try file.getEndPos();
    const buffer_size = std.math.min(file_size, 1024 * 1024); // 1MB buffer
    var buffer = try allocator.alloc(u8, buffer_size);
    defer allocator.free(buffer);

    switch (hash_type) {
        .sha256 => {
            var hasher = std.crypto.hash.sha2.Sha256.init(.{});
            var total_read: u64 = 0;

            while (total_read < file_size) {
                const bytes_read = try file.readAll(buffer);
                if (bytes_read == 0) break;

                hasher.update(buffer[0..bytes_read]);
                total_read += bytes_read;
            }

            const digest = try allocator.alloc(u8, std.crypto.hash.sha2.Sha256.digest_length);
            hasher.final(digest);

            return HashResult{
                .hash_type = .sha256,
                .digest = digest,
            };
        },
        .md5 => {
            var hasher = std.crypto.hash.Md5.init();
            var total_read: u64 = 0;

            while (total_read < file_size) {
                const bytes_read = try file.readAll(buffer);
                if (bytes_read == 0) break;

                hasher.update(buffer[0..bytes_read]);
                total_read += bytes_read;
            }

            const digest = try allocator.alloc(u8, std.crypto.hash.Md5.digest_length);
            hasher.final(digest);

            return HashResult{
                .hash_type = .md5,
                .digest = digest,
            };
        },
        .crc32 => {
            var crc: u32 = 0;
            var total_read: u64 = 0;

            while (total_read < file_size) {
                const bytes_read = try file.readAll(buffer);
                if (bytes_read == 0) break;

                crc = std.hash.Crc32.update(crc, buffer[0..bytes_read]);
                total_read += bytes_read;
            }

            var digest = try allocator.alloc(u8, 4);
            std.mem.writeInt(u32, digest[0..4], crc, .big);

            return HashResult{
                .hash_type = .crc32,
                .digest = digest,
            };
        },
    }
}

pub fn compareHashes(hash1: HashResult, hash2: HashResult) bool {
    if (hash1.hash_type != hash2.hash_type) return false;
    if (hash1.digest.len != hash2.digest.len) return false;

    return std.mem.eql(u8, hash1.digest, hash2.digest);
}

pub fn generateTestPattern(allocator: std.mem.Allocator, size: usize, pattern_type: PatternType) ![]u8 {
    const data = try allocator.alloc(u8, size);

    switch (pattern_type) {
        .sequential => {
            for (data, 0..) |*byte, i| {
                byte.* = @as(u8, @intCast(i % 256));
            }
        },
        .random => {
            var prng = std.Random.DefaultPrng.init(@as(u64, @intCast(std.time.timestamp())));
            const random = prng.random();
            random.bytes(data);
        },
        .alternating => {
            for (data, 0..) |*byte, i| {
                byte.* = if (i % 2 == 0) 0xAA else 0x55;
            }
        },
        .zeros => {
            @memset(data, 0);
        },
        .ones => {
            @memset(data, 0xFF);
        },
    }

    return data;
}

pub const PatternType = enum {
    sequential,
    random,
    alternating,
    zeros,
    ones,
};
