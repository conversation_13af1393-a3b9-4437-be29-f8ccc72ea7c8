const std = @import("std");
const device = @import("device.zig");

pub fn selectDevice(devices: []device.DeviceInfo) !?device.DeviceInfo {
    if (devices.len == 0) return null;

    while (true) {
        // 清屏
        try clearScreen();

        // 显示设备列表
        std.debug.print("检测到以下存储设备:\n\n", .{});
        std.debug.print("┌─────┬──────────────┬──────────────┬──────────────┬──────────────┬──────────┐\n", .{});
        std.debug.print("│ 序号│    设备名    │    路径      │    大小      │    型号      │ 可移动   │\n", .{});
        std.debug.print("├─────┼──────────────┼──────────────┼──────────────┼──────────────┼──────────┤\n", .{});

        for (devices, 0..) |dev, i| {
            const size_str = device.formatSize(dev.size);
            const removable_str = if (dev.removable) "是" else "否";
            std.debug.print("│ {d:>3} │ {s:<12} │ {s:<12} │ {s:<12} │ {s:<12} │ {s:<8} │\n", .{ i + 1, dev.name, dev.path, size_str, dev.model, removable_str });
        }

        std.debug.print("└─────┴──────────────┴──────────────┴──────────────┴──────────────┴──────────┘\n\n", .{});

        std.debug.print("请选择要测试的设备 (输入序号，0退出): ", .{});

        const input = try readUserInput();
        defer std.heap.page_allocator.free(input);

        const choice = std.fmt.parseInt(usize, std.mem.trim(u8, input, " \n\r\t"), 10) catch {
            std.debug.print("无效输入，请输入数字。按回车继续...\n", .{});
            _ = try readUserInput();
            continue;
        };

        if (choice == 0) {
            return null;
        }

        if (choice > devices.len) {
            std.debug.print("序号超出范围，请重新选择。按回车继续...\n", .{});
            _ = try readUserInput();
            continue;
        }

        return devices[choice - 1];
    }
}

pub fn confirmOperation(device_info: device.DeviceInfo) !bool {
    try clearScreen();

    std.debug.print("您选择了以下设备:\n\n", .{});
    std.debug.print("设备名称: {s}\n", .{device_info.name});
    std.debug.print("设备路径: {s}\n", .{device_info.path});
    std.debug.print("设备大小: {s}\n", .{device.formatSize(device_info.size)});
    std.debug.print("设备型号: {s}\n", .{device_info.model});
    std.debug.print("设备厂商: {s}\n", .{device_info.vendor});
    std.debug.print("可移动设备: {s}\n", .{if (device_info.removable) "是" else "否"});

    std.debug.print("\n", .{});
    std.debug.print("⚠️  警告: 此操作将向设备写入测试数据，可能会覆盖现有数据！\n", .{});
    std.debug.print("⚠️  请确保已备份重要数据！\n", .{});
    std.debug.print("\n", .{});

    while (true) {
        std.debug.print("确认继续测试此设备吗? (y/N): ", .{});

        const input = try readUserInput();
        defer std.heap.page_allocator.free(input);

        const trimmed = std.mem.trim(u8, input, " \n\r\t");

        if (std.mem.eql(u8, trimmed, "y") or std.mem.eql(u8, trimmed, "Y") or
            std.mem.eql(u8, trimmed, "yes") or std.mem.eql(u8, trimmed, "YES"))
        {
            return true;
        } else if (std.mem.eql(u8, trimmed, "n") or std.mem.eql(u8, trimmed, "N") or
            std.mem.eql(u8, trimmed, "no") or std.mem.eql(u8, trimmed, "NO") or
            trimmed.len == 0)
        {
            return false;
        } else {
            std.debug.print("请输入 y 或 n。\n", .{});
        }
    }
}

pub fn showProgress(current: u64, total: u64, operation: []const u8) void {
    const percentage = if (total > 0) (current * 100) / total else 0;
    const bar_width = 50;
    const filled = (percentage * bar_width) / 100;

    std.debug.print("\r{s}: [", .{operation});

    var i: usize = 0;
    while (i < bar_width) : (i += 1) {
        if (i < filled) {
            std.debug.print("█", .{});
        } else {
            std.debug.print("░", .{});
        }
    }

    const current_size = device.formatSize(current);
    const total_size = device.formatSize(total);
    std.debug.print("] {d}% ({s}/{s})", .{ percentage, current_size, total_size });
}

pub fn showTestPhase(phase: []const u8) void {
    std.debug.print("\n", .{});
    std.debug.print("┌─────────────────────────────────────────────────────────────┐\n", .{});
    std.debug.print("│ {s:<59} │\n", .{phase});
    std.debug.print("└─────────────────────────────────────────────────────────────┘\n", .{});
}

fn clearScreen() !void {
    // ANSI转义序列清屏
    std.debug.print("\x1B[2J\x1B[H", .{});
}

fn readUserInput() ![]u8 {
    const stdin = std.io.getStdIn().reader();
    var buffer: [256]u8 = undefined;

    if (try stdin.readUntilDelimiterOrEof(buffer[0..], '\n')) |input| {
        return std.heap.page_allocator.dupe(u8, input);
    } else {
        return std.heap.page_allocator.dupe(u8, "");
    }
}

pub fn displayError(message: []const u8) void {
    std.debug.print("\n❌ 错误: {s}\n", .{message});
    std.debug.print("按回车键继续...\n", .{});
    _ = readUserInput() catch {};
}

pub fn displaySuccess(message: []const u8) void {
    std.debug.print("\n✅ {s}\n", .{message});
}

pub fn displayWarning(message: []const u8) void {
    std.debug.print("\n⚠️  警告: {s}\n", .{message});
}
